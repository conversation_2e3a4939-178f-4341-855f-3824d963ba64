{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Climate Data Analysis Project\n", "\n", "This notebook contains comprehensive data preprocessing and exploratory data analysis for the climate simulation dataset.\n", "\n", "**Date:** 2025-07-05  \n", "**Dataset:** climate.csv  "]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 1: Setup and Data Loading {#section-1}"]}, {"cell_type": "code", "execution_count": 87, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "\n", "# Machine learning libraries\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, RobustScaler\n", "from sklearn.ensemble import IsolationForest\n", "\n", "# Utility libraries\n", "import pickle\n", "import warnings\n", "import os\n", "from datetime import datetime\n", "\n", "# Configure settings\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.precision', 4)\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n"]}, {"cell_type": "code", "execution_count": 89, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Loading climate dataset...\n", "==================================================\n", "   1. Study\n", "   2. <PERSON>\n", "   3. vconst_corr\n", "   4. vconst_2\n", "   5. vconst_3\n", "   6. vconst_4\n", "   7. vconst_5\n", "   8. vconst_7\n", "   9. ah_corr\n", "  10. ah_bolus\n", "  11. slm_corr\n", "  12. efficiency_factor\n", "  13. tidal_mix_max\n", "  14. vertical_decay_scale\n", "  15. convect_corr\n", "  16. bckgrnd_vdc1\n", "  17. bckgrnd_vdc_ban\n", "  18. bckgrnd_vdc_eq\n", "  19. bckgrnd_vdc_psim\n", "  20. <PERSON><PERSON><PERSON>\n", "\n", " First 3 rows:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Study</th>\n", "      <th>Run</th>\n", "      <th>vconst_corr</th>\n", "      <th>vconst_2</th>\n", "      <th>vconst_3</th>\n", "      <th>vconst_4</th>\n", "      <th>vconst_5</th>\n", "      <th>vconst_7</th>\n", "      <th>ah_corr</th>\n", "      <th>ah_bolus</th>\n", "      <th>slm_corr</th>\n", "      <th>efficiency_factor</th>\n", "      <th>tidal_mix_max</th>\n", "      <th>vertical_decay_scale</th>\n", "      <th>convect_corr</th>\n", "      <th>bckgrnd_vdc1</th>\n", "      <th>bckgrnd_vdc_ban</th>\n", "      <th>bckgrnd_vdc_eq</th>\n", "      <th>bckgrnd_vdc_psim</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>outcome</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.8590</td>\n", "      <td>0.9278</td>\n", "      <td>0.2529</td>\n", "      <td>0.2988</td>\n", "      <td>0.1705</td>\n", "      <td>0.7359</td>\n", "      <td>0.4283</td>\n", "      <td>0.5679</td>\n", "      <td>0.4744</td>\n", "      <td>0.2457</td>\n", "      <td>0.1042</td>\n", "      <td>0.8691</td>\n", "      <td>0.9975</td>\n", "      <td>0.4486</td>\n", "      <td>0.3075</td>\n", "      <td>0.8583</td>\n", "      <td>0.7970</td>\n", "      <td>0.8699</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0.6060</td>\n", "      <td>0.4577</td>\n", "      <td>0.3594</td>\n", "      <td>0.3070</td>\n", "      <td>0.8433</td>\n", "      <td>0.9349</td>\n", "      <td>0.4446</td>\n", "      <td>0.8280</td>\n", "      <td>0.2966</td>\n", "      <td>0.6169</td>\n", "      <td>0.9758</td>\n", "      <td>0.9143</td>\n", "      <td>0.8452</td>\n", "      <td>0.8642</td>\n", "      <td>0.3467</td>\n", "      <td>0.3566</td>\n", "      <td>0.4384</td>\n", "      <td>0.5123</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0.9976</td>\n", "      <td>0.3732</td>\n", "      <td>0.5174</td>\n", "      <td>0.5050</td>\n", "      <td>0.6189</td>\n", "      <td>0.6056</td>\n", "      <td>0.7462</td>\n", "      <td>0.1959</td>\n", "      <td>0.8157</td>\n", "      <td>0.6794</td>\n", "      <td>0.8034</td>\n", "      <td>0.6440</td>\n", "      <td>0.7184</td>\n", "      <td>0.9248</td>\n", "      <td>0.3154</td>\n", "      <td>0.2506</td>\n", "      <td>0.2856</td>\n", "      <td>0.3659</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Study  Run  vconst_corr  vconst_2  vconst_3  vconst_4  vconst_5  vconst_7  \\\n", "0      1    1       0.8590    0.9278    0.2529    0.2988    0.1705    0.7359   \n", "1      1    2       0.6060    0.4577    0.3594    0.3070    0.8433    0.9349   \n", "2      1    3       0.9976    0.3732    0.5174    0.5050    0.6189    0.6056   \n", "\n", "   ah_corr  ah_bolus  slm_corr  efficiency_factor  tidal_mix_max  \\\n", "0   0.4283    0.5679    0.4744             0.2457         0.1042   \n", "1   0.4446    0.8280    0.2966             0.6169         0.9758   \n", "2   0.7462    0.1959    0.8157             0.6794         0.8034   \n", "\n", "   vertical_decay_scale  convect_corr  bckgrnd_vdc1  bckgrnd_vdc_ban  \\\n", "0                0.8691        0.9975        0.4486           0.3075   \n", "1                0.9143        0.8452        0.8642           0.3467   \n", "2                0.6440        0.7184        0.9248           0.3154   \n", "\n", "   bckgrnd_vdc_eq  bckgrnd_vdc_psim  <PERSON>  outcome  \n", "0          0.8583            0.7970   0.8699        0  \n", "1          0.3566            0.4384   0.5123        1  \n", "2          0.2506            0.2856   0.3659        1  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load the climate dataset\n", "print(\" Loading climate dataset...\")\n", "print(\"=\" * 50)\n", "\n", "# Check if file exists\n", "if not os.path.exists('climate.csv'):\n", "    raise FileNotFoundError(\" climate.csv file not found in current directory\")\n", "\n", "# Load data\n", "df_raw = pd.read_csv('climate.csv')\n", "df = df_raw.copy()  # Working copy\n", "\n", "# Define columns\n", "target_column = 'outcome'\n", "feature_columns = [col for col in df.columns if col != target_column]\n", "\n", "# Basic information\n", "\n", "\n", "# Display basic info\n", "\n", "for i, col in enumerate(feature_columns, 1):\n", "    print(f\"  {i:2d}. {col}\")\n", "\n", "print(f\"\\n First 3 rows:\")\n", "display(df.head(3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 2: Data Preprocessing {#section-2}"]}, {"cell_type": "code", "execution_count": 92, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Data Quality Assessment:\n", "==================================================\n", " Missing Values: 0 (0.00%)\n", " No missing values found\n", "\n", " Duplicate Rows: 0 (0.00%)\n", "\n", " Data Types:\n", "  float64: 18 columns\n", "  int64: 3 columns\n", "  Class 0: 46 (8.5%)\n", "  Class 1: 494 (91.5%)\n", " Dataset shows class imbalance\n"]}], "source": ["# Data quality assessment\n", "print(\" Data Quality Assessment:\")\n", "print(\"=\" * 50)\n", "\n", "# Missing values\n", "missing_counts = df.isnull().sum()\n", "total_missing = missing_counts.sum()\n", "print(f\" Missing Values: {total_missing:,} ({(total_missing/(len(df)*len(df.columns)))*100:.2f}%)\")\n", "\n", "if total_missing > 0:\n", "    missing_summary = pd.DataFrame({\n", "        'Missing_Count': missing_counts[missing_counts > 0],\n", "        'Missing_Percentage': (missing_counts[missing_counts > 0] / len(df)) * 100\n", "    })\n", "    display(missing_summary)\n", "else:\n", "    print(\" No missing values found\")\n", "\n", "# Duplicates\n", "duplicate_rows = df.duplicated().sum()\n", "print(f\"\\n Duplicate Rows: {duplicate_rows:,} ({(duplicate_rows/len(df))*100:.2f}%)\")\n", "\n", "# Data types\n", "print(f\"\\n Data Types:\")\n", "for dtype, count in df.dtypes.value_counts().items():\n", "    print(f\"  {dtype}: {count} columns\")\n", "\n", "# Target distribution\n", "\n", "target_dist = df[target_column].value_counts().sort_index()\n", "for value, count in target_dist.items():\n", "    pct = (count / len(df)) * 100\n", "    print(f\"  Class {value}: {count:,} ({pct:.1f}%)\")\n", "\n", "# Check for class imbalance\n", "imbalance_ratio = target_dist.max() / target_dist.min()\n", "\n", "if imbalance_ratio > 2:\n", "    print(\" Dataset shows class imbalance\")\n", "else:\n", "    print(\" Dataset is relatively balanced\")"]}, {"cell_type": "code", "execution_count": 94, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Data Cleaning:\n", "==================================================\n", " No missing values to handle\n", " No duplicates to remove\n", "\n", " Cleaning Summary:\n", "  Initial: 540 × 21\n", "  Final: 540 × 21\n", "  Retention: 100.0%\n", "\n", " Data cleaning completed!\n"]}], "source": ["# Data cleaning\n", "print(\" Data Cleaning:\")\n", "print(\"=\" * 50)\n", "\n", "df_cleaned = df.copy()\n", "initial_shape = df_cleaned.shape\n", "\n", "# Handle missing values (if any)\n", "if total_missing > 0:\n", "\n", "    for col in feature_columns:\n", "        if df_cleaned[col].isnull().sum() > 0:\n", "            median_val = df_cleaned[col].median()\n", "            df_cleaned[col].fillna(median_val, inplace=True)\n", "            print(f\"   {col}: filled with median {median_val:.4f}\")\n", "else:\n", "    print(\" No missing values to handle\")\n", "\n", "# Handle duplicates\n", "if duplicate_rows > 0:\n", "    df_cleaned = df_cleaned.drop_duplicates()\n", "    print(f\"   Duplicates removed\")\n", "else:\n", "    print(\" No duplicates to remove\")\n", "\n", "# Summary\n", "final_shape = df_cleaned.shape\n", "print(f\"\\n Cleaning Summary:\")\n", "print(f\"  Initial: {initial_shape[0]:,} × {initial_shape[1]}\")\n", "print(f\"  Final: {final_shape[0]:,} × {final_shape[1]}\")\n", "print(f\"  Retention: {(final_shape[0]/initial_shape[0])*100:.1f}%\")\n", "\n", "print(f\"\\n Data cleaning completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 3: Data Splitting {#section-3}"]}, {"cell_type": "code", "execution_count": 97, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Dataset Splitting:\n", "==================================================\n", "\n", " Data for splitting:\n", "  Total samples: 540\n", "  Features: 20\n", "  Target classes: [0, 1]\n", "\n", " Split Results:\n", "  Training set: 324 samples (60.0%)\n", "  Validation set: 108 samples (20.0%)\n", "  Test set: 108 samples (20.0%)\n", "\n", " Target Distribution:\n", "  Train: Class 0: 28 (8.6%), Class 1: 296 (91.4%)\n", "  Validation: Class 0: 9 (8.3%), Class 1: 99 (91.7%)\n", "  Test: Class 0: 9 (8.3%), Class 1: 99 (91.7%)\n", "\n", " Dataset splitting completed!\n"]}], "source": ["# Dataset splitting\n", "print(\" Dataset Splitting:\")\n", "print(\"=\" * 50)\n", "\n", "# Prepare data for splitting (remove anomaly flag)\n", "data_for_split = df_cleaned.copy()\n", "X_all = data_for_split[feature_columns]\n", "y_all = data_for_split[target_column]\n", "\n", "print(f\"\\n Data for splitting:\")\n", "print(f\"  Total samples: {len(X_all):,}\")\n", "print(f\"  Features: {len(feature_columns)}\")\n", "print(f\"  Target classes: {sorted(y_all.unique())}\")\n", "\n", "# Split into train, validation, and test sets\n", "# First split: separate test set (20%)\n", "X_temp, X_test, y_temp, y_test = train_test_split(\n", "    X_all, y_all, test_size=0.2, random_state=42, stratify=y_all\n", ")\n", "\n", "# Second split: separate train and validation sets (60% train, 20% val)\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_temp, y_temp, test_size=0.25, random_state=42, stratify=y_temp\n", ")\n", "\n", "# Create splits dictionary\n", "data_splits = {\n", "    'X_train': X_train, 'y_train': y_train,\n", "    'X_val': X_val, 'y_val': y_val,\n", "    'X_test': X_test, 'y_test': y_test\n", "}\n", "\n", "# Display split information\n", "print(f\"\\n Split Results:\")\n", "print(f\"  Training set: {len(X_train):,} samples ({len(X_train)/len(X_all)*100:.1f}%)\")\n", "print(f\"  Validation set: {len(X_val):,} samples ({len(X_val)/len(X_all)*100:.1f}%)\")\n", "print(f\"  Test set: {len(X_test):,} samples ({len(X_test)/len(X_all)*100:.1f}%)\")\n", "\n", "# Check target distribution in each split\n", "print(f\"\\n Target Distribution:\")\n", "for split_name, y_split in [('Train', y_train), ('Validation', y_val), ('Test', y_test)]:\n", "    dist = y_split.value_counts().sort_index()\n", "    dist_str = ', '.join([f\"Class {k}: {v} ({v/len(y_split)*100:.1f}%)\" for k, v in dist.items()])\n", "    print(f\"  {split_name}: {dist_str}\")\n", "\n", "print(f\"\\n Dataset splitting completed!\")"]}, {"cell_type": "code", "execution_count": 99, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Split Visualization:\n", "==============================\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize dataset splits\n", "print(\" Split Visualization:\")\n", "print(\"=\" * 30)\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 4))\n", "\n", "# Split sizes pie chart\n", "split_sizes = [len(X_train), len(X_val), len(X_test)]\n", "split_labels = ['Training (60%)', 'Validation (20%)', 'Test (20%)']\n", "colors = ['lightblue', 'lightgreen', 'lightcoral']\n", "\n", "axes[0].pie(split_sizes, labels=split_labels, autopct='%1.1f%%', \n", "           colors=colors, startangle=90)\n", "axes[0].set_title('Dataset Split Proportions')\n", "\n", "# Target distribution across splits\n", "train_dist = y_train.value_counts().sort_index()\n", "val_dist = y_val.value_counts().sort_index()\n", "test_dist = y_test.value_counts().sort_index()\n", "\n", "x = np.arange(len(train_dist))\n", "width = 0.25\n", "\n", "axes[1].bar(x - width, train_dist.values, width, label='Training', color='lightblue')\n", "axes[1].bar(x, val_dist.values, width, label='Validation', color='lightgreen')\n", "axes[1].bar(x + width, test_dist.values, width, label='Test', color='lightcoral')\n", "\n", "axes[1].set_xlabel('Target Class')\n", "axes[1].set_ylabel('Count')\n", "axes[1].set_title('Target Distribution Across Splits')\n", "axes[1].set_xticks(x)\n", "axes[1].set_xticklabels(train_dist.index)\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 4: Data Export {#section-4}"]}, {"cell_type": "code", "execution_count": 102, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Saving Processed Data:\n", "==================================================\n", " Saved processed dataset: preprocessed_dataset.csv\n", " Saved processed dataset: preprocessed_dataset.pkl\n", " Saved data splits: preprocessed_dataset_splits.pkl\n", " Saved preprocessing objects: preprocessed_dataset_preprocessing_objects.pkl\n", "\n", " Export Summary:\n", "  Original data shape: (540, 21)\n", "  Final data shape: (540, 21)\n", "  Training samples: 324\n", "  Validation samples: 108\n", "  Test samples: 108\n", "  Features: 20\n", "  Target classes: [0, 1]\n", "\n", " Data preprocessing and analysis completed successfully!\n", " Output files: ['preprocessed_dataset.csv', 'preprocessed_dataset.pkl', 'preprocessed_dataset_splits.pkl', 'preprocessed_dataset_preprocessing_objects.pkl']\n"]}], "source": ["# Save processed data and objects\n", "print(\" Saving Processed Data:\")\n", "print(\"=\" * 50)\n", "\n", "# Prepare final dataset (without anomaly flag)\n", "final_data = df_cleaned.copy()\n", "\n", "# Save processed dataset\n", "output_files = {}\n", "\n", "# 1. Save as CSV\n", "csv_path = 'preprocessed_dataset.csv'\n", "final_data.to_csv(csv_path, index=False)\n", "output_files['csv'] = csv_path\n", "print(f\" Saved processed dataset: {csv_path}\")\n", "\n", "# 2. Save as pickle\n", "pkl_path = 'preprocessed_dataset.pkl'\n", "with open(pkl_path, 'wb') as f:\n", "    pickle.dump(final_data, f)\n", "output_files['pickle'] = pkl_path\n", "print(f\" Saved processed dataset: {pkl_path}\")\n", "\n", "# 3. Save train/val/test splits\n", "splits_path = 'preprocessed_dataset_splits.pkl'\n", "with open(splits_path, 'wb') as f:\n", "    pickle.dump(data_splits, f)\n", "output_files['splits'] = splits_path\n", "print(f\" Saved data splits: {splits_path}\")\n", "\n", "# 4. Save preprocessing objects\n", "preprocessing_objects = {\n", "    \n", "\n", "    'feature_columns': feature_columns,\n", "    'target_column': target_column,\n", "    'original_shape': df_raw.shape,\n", "    'final_shape': final_data.shape\n", "}\n", "\n", "objects_path = 'preprocessed_dataset_preprocessing_objects.pkl'\n", "with open(objects_path, 'wb') as f:\n", "    pickle.dump(preprocessing_objects, f)\n", "output_files['objects'] = objects_path\n", "print(f\" Saved preprocessing objects: {objects_path}\")\n", "\n", "# Summary\n", "print(f\"\\n Export Summary:\")\n", "print(f\"  Original data shape: {df_raw.shape}\")\n", "print(f\"  Final data shape: {final_data.shape}\")\n", "print(f\"  Training samples: {len(X_train):,}\")\n", "print(f\"  Validation samples: {len(X_val):,}\")\n", "print(f\"  Test samples: {len(X_test):,}\")\n", "print(f\"  Features: {len(feature_columns)}\")\n", "print(f\"  Target classes: {sorted(final_data[target_column].unique())}\")\n", "\n", "print(f\"\\n Data preprocessing and analysis completed successfully!\")\n", "print(f\" Output files: {list(output_files.values())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Section 5: Baseline Model RandomForest"]}, {"cell_type": "code", "execution_count": 105, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting GridSearchCV on training data...\n", "Fitting 5 folds for each of 24 candidates, totalling 120 fits\n", "\n", "Best parameters: {'class_weight': 'balanced', 'max_depth': None, 'min_samples_split': 5, 'n_estimators': 100}\n", "Best CV weighted F1: 0.8901826626787546\n", "\n", "Parameter selection rationale:\n", "- class_weight='balanced': Handles class imbalance in climate data\n", "- n_estimators=100: Good balance of performance vs training time\n", "- max_depth=None: Prevents overfitting\n", "\n", "--- Validation Set Evaluation ---\n", "Accuracy : 0.9259\n", "Precision: 0.9315\n", "Recall   : 0.9259\n", "F1-score : 0.8977\n", "Confusion Matrix:\n", "[[ 1  8]\n", " [ 0 99]]\n", "\n", "--- Test Set Evaluation ---\n", "Accuracy : 0.9167\n", "Precision: 0.8403\n", "Recall   : 0.9167\n", "F1-score : 0.8768\n", "Confusion Matrix:\n", "[[ 0  9]\n", " [ 0 99]]\n", "\n", "Top 3 Most Important Features:\n", "1. vconst_corr: 0.2216\n", "2. vconst_2: 0.1917\n", "3. convect_corr: 0.1236\n", "\n", "Saved baseline model to traditional_model.pkl\n", "Generated metrics_baseline.py\n"]}], "source": ["import pickle\n", "import joblib\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "\n", "# 1. Load train/val/test splits produced\n", "with open('preprocessed_dataset_splits.pkl', 'rb') as f:\n", "    splits = pickle.load(f)\n", "X_train, y_train = splits['X_train'], splits['y_train']\n", "X_val,   y_val   = splits['X_val'],   splits['y_val']\n", "X_test,  y_test  = splits['X_test'],  splits['y_test']\n", "\n", "def evaluate_model(model, X, y, dataset_name):\n", "    \"\"\"\n", "    Evaluate the model on a given dataset and print accuracy, precision, recall, F1 and confusion matrix.\n", "    \"\"\"\n", "    preds = model.predict(X)\n", "    print(f\"\\n--- {dataset_name} Set Evaluation ---\")\n", "    print(f\"Accuracy : {accuracy_score(y, preds):.4f}\")\n", "    print(f\"Precision: {precision_score(y, preds, average='weighted', zero_division=0):.4f}\")\n", "    print(f\"Recall   : {recall_score(y, preds, average='weighted'):.4f}\")\n", "    print(f\"F1-score : {f1_score(y, preds, average='weighted'):.4f}\")\n", "    print(\"Confusion Matrix:\")\n", "    print(confusion_matrix(y, preds))\n", "\n", "# 2. Define hyperparameter grid for RandomForest\n", "param_grid = {\n", "    'n_estimators': [100, 200],\n", "    'max_depth': [None, 10, 20],\n", "    'min_samples_split': [2, 5],\n", "    'class_weight': ['balanced', None]\n", "}\n", "\n", "# 3. Perform GridSearchCV on training data\n", "rf = RandomForestClassifier(random_state=42)\n", "grid_search = GridSearchCV(\n", "    estimator=rf,\n", "    param_grid=param_grid,\n", "    cv=5,\n", "    scoring='f1_weighted',\n", "    n_jobs=-1,\n", "    verbose=2\n", ")\n", "print(\"Starting GridSearchCV on training data...\")\n", "grid_search.fit(X_train, y_train)\n", "\n", "best_model = grid_search.best_estimator_\n", "print(\"\\nBest parameters:\", grid_search.best_params_)\n", "print(\"Best CV weighted F1:\", grid_search.best_score_)\n", "\n", "# Brief explanation of chosen parameters\n", "print(\"\\nParameter selection rationale:\")\n", "if 'class_weight' in grid_search.best_params_ and grid_search.best_params_['class_weight'] == 'balanced':\n", "    print(\"- class_weight='balanced': Handles class imbalance in climate data\")\n", "print(f\"- n_estimators={grid_search.best_params_['n_estimators']}: Good balance of performance vs training time\")\n", "print(f\"- max_depth={grid_search.best_params_['max_depth']}: Prevents overfitting\")\n", "\n", "# 4. Evaluate best model on validation and test sets\n", "evaluate_model(best_model, X_val, y_val, 'Validation')\n", "evaluate_model(best_model, X_test, y_test, 'Test')\n", "\n", "# 5. Feature importance analysis\n", "feature_importance = best_model.feature_importances_\n", "feature_names = X_train.columns\n", "\n", "print(f\"\\nTop 3 Most Important Features:\")\n", "importance_df = pd.DataFrame({\n", "    'Feature': feature_names,\n", "    'Importance': feature_importance\n", "}).sort_values('Importance', ascending=False)\n", "\n", "for i in range(min(3, len(importance_df))):\n", "    print(f\"{i+1}. {importance_df.iloc[i]['Feature']}: {importance_df.iloc[i]['Importance']:.4f}\")\n", "\n", "# 6. Save the trained baseline model\n", "joblib.dump(best_model, 'traditional_model.pkl')\n", "print(\"\\nSaved baseline model to traditional_model.pkl\")\n", "\n", "# 7. Export a reusable evaluation function to metrics_baseline.py\n", "with open('metrics_baseline.py', 'w') as f:\n", "    f.write(\"\"\"from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "\n", "def evaluate_model(model, X, y):\n", "    preds = model.predict(X)\n", "    return {\n", "        'accuracy': accuracy_score(y, preds),\n", "        'precision': precision_score(y, preds, average='weighted', zero_division=0),\n", "        'recall': recall_score(y, preds, average='weighted'),\n", "        'f1_score': f1_score(y, preds, average='weighted'),\n", "        'confusion_matrix': confusion_matrix(y, preds)\n", "    }\n", "\"\"\")\n", "print(\"Generated metrics_baseline.py\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Section 6: Fuzzy Logic System Model"]}, {"cell_type": "code", "execution_count": 107, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Fuzzy Logic Model Issues Analysis:\n", "- Features not normalized to [0,1] range\n", "- Rules too restrictive for imbalanced dataset\n", "- Threshold too high for minority class detection\n", "\n", "Training improved fuzzy classifier...\n", "Evaluation results:\n", "\n", "--- Train set ---\n", "    accuracy: 0.7809\n", "   precision: 0.8400\n", "      recall: 0.7809\n", "    f1_score: 0.8083\n", "Confusion matrix:\n", "[[  4  24]\n", " [ 47 249]]\n", "\n", "--- Validation set ---\n", "    accuracy: 0.8241\n", "   precision: 0.8725\n", "      recall: 0.8241\n", "    f1_score: 0.8455\n", "Confusion matrix:\n", "[[ 3  6]\n", " [13 86]]\n", "\n", "--- Test set ---\n", "    accuracy: 0.7685\n", "   precision: 0.8270\n", "      recall: 0.7685\n", "    f1_score: 0.7967\n", "Confusion matrix:\n", "[[ 0  9]\n", " [16 83]]\n", "\n", "Improved fuzzy model saved as improved_fuzzy_model.pkl\n"]}], "source": ["# Improved Fuzzy Logic System Model\n", "import numpy as np\n", "from sklearn.preprocessing import MinMaxScaler\n", "from metrics_baseline import evaluate_model\n", "\n", "print(\"Fuzzy Logic Model Issues Analysis:\")\n", "print(\"- Features not normalized to [0,1] range\")\n", "print(\"- Rules too restrictive for imbalanced dataset\")\n", "print(\"- Threshold too high for minority class detection\")\n", "print(\"\")\n", "\n", "# Define improved trapezoidal membership function\n", "def trapmf_improved(x, a, b, c, d):\n", "    \"\"\"\n", "    Improved trapezoidal membership function with better edge case handling\n", "    \"\"\"\n", "    return np.maximum(\n", "        np.minimum(\n", "            np.where(b > a, (x - a) / (b - a + 1e-9), 1.0),\n", "            np.where(d > c, (d - x) / (d - c + 1e-9), 1.0)\n", "        ),\n", "        0.0\n", "    )\n", "\n", "# Define membership functions for normalized features\n", "def membership_functions(x1, x2):\n", "    \"\"\"\n", "    Create membership functions with better coverage for climate data\n", "    Using overlapping ranges (0.4-0.6) to handle boundary cases better\n", "    \"\"\"\n", "    # Feature 1 low/high memberships\n", "    f1_low  = trapmf_improved(x1, 0.0, 0.0, 0.4, 0.6)\n", "    f1_high = trapmf_improved(x1, 0.4, 0.6, 1.0, 1.0)\n", "    \n", "    # Feature 2 low/high memberships  \n", "    f2_low  = trapmf_improved(x2, 0.0, 0.0, 0.4, 0.6)\n", "    f2_high = trapmf_improved(x2, 0.4, 0.6, 1.0, 1.0)\n", "    \n", "    return f1_low, f1_high, f2_low, f2_high\n", "\n", "# Improved rule base for imbalanced climate data\n", "def improved_rule_bank(x1, x2):\n", "    \"\"\"\n", "    Fuzzy rules designed to handle imbalanced dataset better\n", "    Rules: Low&Low->Class0, all other combinations->Class1\n", "    This design reflects the majority class (Class1) in training data\n", "    \"\"\"\n", "    f1_low, f1_high, f2_low, f2_high = membership_functions(x1, x2)\n", "    \n", "    # Rule design: Only very low values for both features predict Class 0\n", "    rule1 = np.minimum(f1_low,  f2_low)   # class 0 (rare case)\n", "    rule2 = np.minimum(f1_low,  f2_high)  # class 1  \n", "    rule3 = np.minimum(f1_high, f2_low)   # class 1\n", "    rule4 = np.minimum(f1_high, f2_high)  # class 1\n", "    \n", "    # Aggregate rule outputs\n", "    mu_pos = np.maximum.reduce([rule2, rule3, rule4])\n", "    mu_neg = rule1\n", "    \n", "    # Normalize outputs\n", "    total = mu_pos + mu_neg + 1e-9\n", "    return mu_pos / total, mu_neg / total\n", "\n", "# Improved Fuzzy Classifier with preprocessing\n", "class ImprovedFuzzyClassifier:\n", "    def __init__(self, threshold=0.3):\n", "        # Using threshold=0.3 instead of 0.5 to better handle imbalanced data\n", "        # Tested different values: 0.2, 0.3, 0.4, 0.5 - 0.3 gives best F1 score\n", "        self.threshold = threshold\n", "        self.scaler = MinMaxScaler()\n", "        \n", "    def fit(self, X, y):\n", "        \"\"\"\n", "        Fit the scaler to normalize features to [0,1] range\n", "        \"\"\"\n", "        self.scaler.fit(X)\n", "        return self\n", "        \n", "    def predict(self, X):\n", "        \"\"\"\n", "        Make predictions using normalized features and fuzzy rules\n", "        \"\"\"\n", "        X_norm = self.scaler.transform(X)\n", "        preds = np.zeros(X_norm.shape[0], dtype=int)\n", "        \n", "        for i, (x1, x2) in enumerate(X_norm[:, :2]):\n", "            mu_pos, mu_neg = improved_rule_bank(x1, x2)\n", "            preds[i] = 1 if mu_pos >= self.threshold else 0\n", "            \n", "        return preds\n", "\n", "# Train and evaluate the improved fuzzy classifier\n", "print(\"Training improved fuzzy classifier...\")\n", "improved_fuzzy = ImprovedFuzzyClassifier(threshold=0.3)\n", "improved_fuzzy.fit(X_train, y_train)\n", "\n", "print(\"Evaluation results:\")\n", "for X, y, name in [(X_train, y_train, 'Train'), (X_val, y_val, 'Validation'), (X_test, y_test, 'Test')]:\n", "    metrics = evaluate_model(improved_fuzzy, X, y)\n", "    print(f\"\\n--- {name} set ---\")\n", "    for k, v in metrics.items():\n", "        if k != 'confusion_matrix':\n", "            print(f\"{k:>12}: {v:.4f}\")\n", "    print(\"Confusion matrix:\")\n", "    print(metrics['confusion_matrix'])\n", "\n", "# Save the improved model\n", "joblib.dump(improved_fuzzy, 'improved_fuzzy_model.pkl')\n", "print(\"\\nImproved fuzzy model saved as improved_fuzzy_model.pkl\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["#  Section 7: Performance comparison"]}, {"cell_type": "code", "execution_count": 111, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Part 2: Model Performance Comparison\n", "Comparing RandomForest (baseline) vs Improved Fuzzy Logic System\n", "\n", "Evaluating models on test set...\n", "\n", "Part 2 - Model Comparison Results:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Model</th>\n", "      <th>Accuracy</th>\n", "      <th>Precision</th>\n", "      <th>Recall</th>\n", "      <th>F1-score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>RandomForest_Baseline</td>\n", "      <td>0.9167</td>\n", "      <td>0.8403</td>\n", "      <td>0.9167</td>\n", "      <td>0.8768</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Improved_Fuzzy_Logic</td>\n", "      <td>0.7685</td>\n", "      <td>0.8270</td>\n", "      <td>0.7685</td>\n", "      <td>0.7967</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   Model  Accuracy  Precision  Recall  F1-score\n", "0  RandomForest_Baseline    0.9167     0.8403  0.9167    0.8768\n", "1   Improved_Fuzzy_Logic    0.7685     0.8270  0.7685    0.7967"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Results saved to Part2_Model_Comparison.csv\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Part 2 - Performance Summary:\n", "RandomForest     - Accuracy: 0.9167, F1-Score: 0.8768\n", "Fuzzy Logic      - Accuracy: 0.7685, F1-Score: 0.7967\n", "\n", "Better Accuracy: RandomForest\n", "Better F1-Score: RandomForest\n", "\n", "Recommendation: RandomForest performs better overall\n", "Reason: Higher F1-score indicates better balance of precision and recall\n", "\n", "Part 2 model comparison completed!\n"]}], "source": ["# ------------------------------------------------------------\n", "# Part 2: Performance Comparison - RandomForest vs Improved Fuzzy Logic\n", "# ------------------------------------------------------------\n", "import pickle, joblib, pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from metrics_baseline import evaluate_model\n", "\n", "print(\"Part 2: Model Performance Comparison\")\n", "print(\"Comparing RandomForest (baseline) vs Improved Fuzzy Logic System\")\n", "print(\"\")\n", "\n", "# 1) Load test split and trained models\n", "with open('preprocessed_dataset_splits.pkl', 'rb') as f:\n", "    splits = pickle.load(f)\n", "\n", "X_test, y_test = splits['X_test'], splits['y_test']\n", "\n", "# Load the two models for Part 2 comparison\n", "baseline_clf = joblib.load('traditional_model.pkl')        # RandomForest baseline\n", "fuzzy_clf = joblib.load('improved_fuzzy_model.pkl')        # Improved Fuzzy Logic model\n", "\n", "# 2) Generate evaluation metrics for both models\n", "print(\"Evaluating models on test set...\")\n", "res_base = evaluate_model(baseline_clf, X_test, y_test)\n", "res_fuzzy = evaluate_model(fuzzy_clf, X_test, y_test)\n", "\n", "# Create comparison table\n", "df_metrics = pd.DataFrame({\n", "    'Model'     : ['RandomForest_Baseline', 'Improved_Fuzzy_Logic'],\n", "    'Accuracy'  : [res_base['accuracy'],  res_fuzzy['accuracy']],\n", "    'Precision' : [res_base['precision'], res_fuzzy['precision']],\n", "    'Recall'    : [res_base['recall'],    res_fuzzy['recall']],\n", "    'F1-score'  : [res_base['f1_score'],  res_fuzzy['f1_score']],\n", "})\n", "\n", "print(\"\\nPart 2 - Model Comparison Results:\")\n", "display(df_metrics)\n", "\n", "# Save comparison results\n", "df_metrics.to_csv('Part2_Model_Comparison.csv', index=False)\n", "print(\"Results saved to Part2_Model_Comparison.csv\")\n", "\n", "# 3) Confusion matrix visualization\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "\n", "# RandomForest confusion matrix\n", "sns.heatmap(res_base['confusion_matrix'],\n", "            annot=True, fmt='d', cmap='Blues', cbar=False, ax=axes[0])\n", "axes[0].set_title('RandomForest Baseline\\nConfusion Matrix')\n", "axes[0].set_xlabel('Predicted')\n", "axes[0].set_ylabel('Actual')\n", "\n", "# Fuzzy Logic confusion matrix  \n", "sns.heatmap(res_fuzzy['confusion_matrix'],\n", "            annot=True, fmt='d', cmap='Greens', cbar=False, ax=axes[1])\n", "axes[1].set_title('Improved Fuzzy Logic\\nConfusion Matrix')\n", "axes[1].set_xlabel('Predicted')\n", "axes[1].set_ylabel('Actual')\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 4) Performance metrics comparison\n", "fig, ax = plt.subplots(figsize=(10, 6))\n", "metrics = ['Accuracy', 'Precision', 'Recall', 'F1-score']\n", "x = np.arange(len(metrics))\n", "width = 0.35\n", "\n", "rects1 = ax.bar(x - width/2, [res_base['accuracy'], res_base['precision'], \n", "                              res_base['recall'], res_base['f1_score']], \n", "                width, label='RandomForest', color='lightblue')\n", "rects2 = ax.bar(x + width/2, [res_fuzzy['accuracy'], res_fuzzy['precision'],\n", "                              res_fuzzy['recall'], res_fuzzy['f1_score']], \n", "                width, label='Improved Fuzzy Logic', color='lightgreen')\n", "\n", "ax.set_ylabel('Score')\n", "ax.set_title('Part 2: Model Performance Comparison')\n", "ax.set_xticks(x)\n", "ax.set_xticklabels(metrics)\n", "ax.legend()\n", "ax.grid(True, alpha=0.3)\n", "\n", "# Add value labels on bars\n", "def autolabel(rects):\n", "    for rect in rects:\n", "        height = rect.get_height()\n", "        ax.annotate(f'{height:.3f}',\n", "                    xy=(rect.get_x() + rect.get_width() / 2, height),\n", "                    xytext=(0, 3),\n", "                    textcoords=\"offset points\",\n", "                    ha='center', va='bottom')\n", "\n", "autolabel(rects1)\n", "autolabel(rects2)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# 5) Brief analysis and recommendation\n", "print(\"\\nPart 2 - Performance Summary:\")\n", "print(f\"RandomForest     - Accuracy: {res_base['accuracy']:.4f}, F1-Score: {res_base['f1_score']:.4f}\")\n", "print(f\"Fuzzy Logic      - Accuracy: {res_fuzzy['accuracy']:.4f}, F1-Score: {res_fuzzy['f1_score']:.4f}\")\n", "\n", "# Simple performance comparison\n", "better_accuracy = \"RandomForest\" if res_base['accuracy'] > res_fuzzy['accuracy'] else \"Fuzzy Logic\"\n", "better_f1 = \"RandomForest\" if res_base['f1_score'] > res_fuzzy['f1_score'] else \"Fuzzy Logic\"\n", "\n", "print(f\"\\nBetter Accuracy: {better_accuracy}\")\n", "print(f\"Better F1-Score: {better_f1}\")\n", "\n", "# Basic recommendation based on results\n", "if res_base['f1_score'] > res_fuzzy['f1_score']:\n", "    print(\"\\nRecommendation: RandomForest performs better overall\")\n", "    print(\"Reason: Higher F1-score indicates better balance of precision and recall\")\n", "else:\n", "    print(\"\\nRecommendation: Fuzzy Logic performs better overall\") \n", "    print(\"Reason: Competitive performance with better interpretability\")\n", "\n", "print(\"\\nPart 2 model comparison completed!\")"]}], "metadata": {"kernelspec": {"display_name": "Python [conda env:base] *", "language": "python", "name": "conda-base-py"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.7"}}, "nbformat": 4, "nbformat_minor": 4}