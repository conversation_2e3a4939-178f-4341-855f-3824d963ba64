# Import required libraries
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import plotly.express as px
import plotly.graph_objects as go

# Machine learning libraries
from sklearn.model_selection import train_test_split
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import IsolationForest

# Utility libraries
import pickle
import warnings
import os
from datetime import datetime

# Configure settings
warnings.filterwarnings('ignore')
pd.set_option('display.max_columns', None)
pd.set_option('display.precision', 4)
plt.style.use('default')
sns.set_palette("husl")


# Load the climate dataset
print(" Loading climate dataset...")
print("=" * 50)

# Check if file exists
if not os.path.exists('climate.csv'):
    raise FileNotFoundError(" climate.csv file not found in current directory")

# Load data
df_raw = pd.read_csv('climate.csv')
df = df_raw.copy()  # Working copy

# Define columns
target_column = 'outcome'
feature_columns = [col for col in df.columns if col != target_column]

# Basic information


# Display basic info

for i, col in enumerate(feature_columns, 1):
    print(f"  {i:2d}. {col}")

print(f"\n First 3 rows:")
display(df.head(3))

# Data quality assessment
print(" Data Quality Assessment:")
print("=" * 50)

# Missing values
missing_counts = df.isnull().sum()
total_missing = missing_counts.sum()
print(f" Missing Values: {total_missing:,} ({(total_missing/(len(df)*len(df.columns)))*100:.2f}%)")

if total_missing > 0:
    missing_summary = pd.DataFrame({
        'Missing_Count': missing_counts[missing_counts > 0],
        'Missing_Percentage': (missing_counts[missing_counts > 0] / len(df)) * 100
    })
    display(missing_summary)
else:
    print(" No missing values found")

# Duplicates
duplicate_rows = df.duplicated().sum()
print(f"\n Duplicate Rows: {duplicate_rows:,} ({(duplicate_rows/len(df))*100:.2f}%)")

# Data types
print(f"\n Data Types:")
for dtype, count in df.dtypes.value_counts().items():
    print(f"  {dtype}: {count} columns")

# Target distribution

target_dist = df[target_column].value_counts().sort_index()
for value, count in target_dist.items():
    pct = (count / len(df)) * 100
    print(f"  Class {value}: {count:,} ({pct:.1f}%)")

# Check for class imbalance
imbalance_ratio = target_dist.max() / target_dist.min()

if imbalance_ratio > 2:
    print(" Dataset shows class imbalance")
else:
    print(" Dataset is relatively balanced")

# Data cleaning
print(" Data Cleaning:")
print("=" * 50)

df_cleaned = df.copy()
initial_shape = df_cleaned.shape

# Handle missing values (if any)
if total_missing > 0:

    for col in feature_columns:
        if df_cleaned[col].isnull().sum() > 0:
            median_val = df_cleaned[col].median()
            df_cleaned[col].fillna(median_val, inplace=True)
            print(f"   {col}: filled with median {median_val:.4f}")
else:
    print(" No missing values to handle")

# Handle duplicates
if duplicate_rows > 0:
    df_cleaned = df_cleaned.drop_duplicates()
    print(f"   Duplicates removed")
else:
    print(" No duplicates to remove")

# Summary
final_shape = df_cleaned.shape
print(f"\n Cleaning Summary:")
print(f"  Initial: {initial_shape[0]:,} × {initial_shape[1]}")
print(f"  Final: {final_shape[0]:,} × {final_shape[1]}")
print(f"  Retention: {(final_shape[0]/initial_shape[0])*100:.1f}%")

print(f"\n Data cleaning completed!")

# Dataset splitting
print(" Dataset Splitting:")
print("=" * 50)

# Prepare data for splitting (remove anomaly flag)
data_for_split = df_cleaned.copy()
X_all = data_for_split[feature_columns]
y_all = data_for_split[target_column]

print(f"\n Data for splitting:")
print(f"  Total samples: {len(X_all):,}")
print(f"  Features: {len(feature_columns)}")
print(f"  Target classes: {sorted(y_all.unique())}")

# Split into train, validation, and test sets
# First split: separate test set (20%)
X_temp, X_test, y_temp, y_test = train_test_split(
    X_all, y_all, test_size=0.2, random_state=42, stratify=y_all
)

# Second split: separate train and validation sets (60% train, 20% val)
X_train, X_val, y_train, y_val = train_test_split(
    X_temp, y_temp, test_size=0.25, random_state=42, stratify=y_temp
)

# Create splits dictionary
data_splits = {
    'X_train': X_train, 'y_train': y_train,
    'X_val': X_val, 'y_val': y_val,
    'X_test': X_test, 'y_test': y_test
}

# Display split information
print(f"\n Split Results:")
print(f"  Training set: {len(X_train):,} samples ({len(X_train)/len(X_all)*100:.1f}%)")
print(f"  Validation set: {len(X_val):,} samples ({len(X_val)/len(X_all)*100:.1f}%)")
print(f"  Test set: {len(X_test):,} samples ({len(X_test)/len(X_all)*100:.1f}%)")

# Check target distribution in each split
print(f"\n Target Distribution:")
for split_name, y_split in [('Train', y_train), ('Validation', y_val), ('Test', y_test)]:
    dist = y_split.value_counts().sort_index()
    dist_str = ', '.join([f"Class {k}: {v} ({v/len(y_split)*100:.1f}%)" for k, v in dist.items()])
    print(f"  {split_name}: {dist_str}")

print(f"\n Dataset splitting completed!")

# Visualize dataset splits
print(" Split Visualization:")
print("=" * 30)

fig, axes = plt.subplots(1, 2, figsize=(12, 4))

# Split sizes pie chart
split_sizes = [len(X_train), len(X_val), len(X_test)]
split_labels = ['Training (60%)', 'Validation (20%)', 'Test (20%)']
colors = ['lightblue', 'lightgreen', 'lightcoral']

axes[0].pie(split_sizes, labels=split_labels, autopct='%1.1f%%', 
           colors=colors, startangle=90)
axes[0].set_title('Dataset Split Proportions')

# Target distribution across splits
train_dist = y_train.value_counts().sort_index()
val_dist = y_val.value_counts().sort_index()
test_dist = y_test.value_counts().sort_index()

x = np.arange(len(train_dist))
width = 0.25

axes[1].bar(x - width, train_dist.values, width, label='Training', color='lightblue')
axes[1].bar(x, val_dist.values, width, label='Validation', color='lightgreen')
axes[1].bar(x + width, test_dist.values, width, label='Test', color='lightcoral')

axes[1].set_xlabel('Target Class')
axes[1].set_ylabel('Count')
axes[1].set_title('Target Distribution Across Splits')
axes[1].set_xticks(x)
axes[1].set_xticklabels(train_dist.index)
axes[1].legend()
axes[1].grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Save processed data and objects
print(" Saving Processed Data:")
print("=" * 50)

# Prepare final dataset (without anomaly flag)
final_data = df_cleaned.copy()

# Save processed dataset
output_files = {}

# 1. Save as CSV
csv_path = 'preprocessed_dataset.csv'
final_data.to_csv(csv_path, index=False)
output_files['csv'] = csv_path
print(f" Saved processed dataset: {csv_path}")

# 2. Save as pickle
pkl_path = 'preprocessed_dataset.pkl'
with open(pkl_path, 'wb') as f:
    pickle.dump(final_data, f)
output_files['pickle'] = pkl_path
print(f" Saved processed dataset: {pkl_path}")

# 3. Save train/val/test splits
splits_path = 'preprocessed_dataset_splits.pkl'
with open(splits_path, 'wb') as f:
    pickle.dump(data_splits, f)
output_files['splits'] = splits_path
print(f" Saved data splits: {splits_path}")

# 4. Save preprocessing objects
preprocessing_objects = {
    

    'feature_columns': feature_columns,
    'target_column': target_column,
    'original_shape': df_raw.shape,
    'final_shape': final_data.shape
}

objects_path = 'preprocessed_dataset_preprocessing_objects.pkl'
with open(objects_path, 'wb') as f:
    pickle.dump(preprocessing_objects, f)
output_files['objects'] = objects_path
print(f" Saved preprocessing objects: {objects_path}")

# Summary
print(f"\n Export Summary:")
print(f"  Original data shape: {df_raw.shape}")
print(f"  Final data shape: {final_data.shape}")
print(f"  Training samples: {len(X_train):,}")
print(f"  Validation samples: {len(X_val):,}")
print(f"  Test samples: {len(X_test):,}")
print(f"  Features: {len(feature_columns)}")
print(f"  Target classes: {sorted(final_data[target_column].unique())}")

print(f"\n Data preprocessing and analysis completed successfully!")
print(f" Output files: {list(output_files.values())}")

import pickle
import joblib
from sklearn.ensemble import RandomForestClassifier
from sklearn.model_selection import GridSearchCV
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

# 1. Load train/val/test splits produced
with open('preprocessed_dataset_splits.pkl', 'rb') as f:
    splits = pickle.load(f)
X_train, y_train = splits['X_train'], splits['y_train']
X_val,   y_val   = splits['X_val'],   splits['y_val']
X_test,  y_test  = splits['X_test'],  splits['y_test']

def evaluate_model(model, X, y, dataset_name):
    """
    Evaluate the model on a given dataset and print accuracy, precision, recall, F1 and confusion matrix.
    """
    preds = model.predict(X)
    print(f"\n--- {dataset_name} Set Evaluation ---")
    print(f"Accuracy : {accuracy_score(y, preds):.4f}")
    print(f"Precision: {precision_score(y, preds, average='weighted', zero_division=0):.4f}")
    print(f"Recall   : {recall_score(y, preds, average='weighted'):.4f}")
    print(f"F1-score : {f1_score(y, preds, average='weighted'):.4f}")
    print("Confusion Matrix:")
    print(confusion_matrix(y, preds))

# 2. Define hyperparameter grid for RandomForest
param_grid = {
    'n_estimators': [100, 200],
    'max_depth': [None, 10, 20],
    'min_samples_split': [2, 5],
    'class_weight': ['balanced', None]
}

# 3. Perform GridSearchCV on training data
rf = RandomForestClassifier(random_state=42)
grid_search = GridSearchCV(
    estimator=rf,
    param_grid=param_grid,
    cv=5,
    scoring='f1_weighted',
    n_jobs=-1,
    verbose=2
)
print("Starting GridSearchCV on training data...")
grid_search.fit(X_train, y_train)

best_model = grid_search.best_estimator_
print("\nBest parameters:", grid_search.best_params_)
print("Best CV weighted F1:", grid_search.best_score_)

# Brief explanation of chosen parameters
print("\nParameter selection rationale:")
if 'class_weight' in grid_search.best_params_ and grid_search.best_params_['class_weight'] == 'balanced':
    print("- class_weight='balanced': Handles class imbalance in climate data")
print(f"- n_estimators={grid_search.best_params_['n_estimators']}: Good balance of performance vs training time")
print(f"- max_depth={grid_search.best_params_['max_depth']}: Prevents overfitting")

# 4. Evaluate best model on validation and test sets
evaluate_model(best_model, X_val, y_val, 'Validation')
evaluate_model(best_model, X_test, y_test, 'Test')

# 5. Feature importance analysis
feature_importance = best_model.feature_importances_
feature_names = X_train.columns

print(f"\nTop 3 Most Important Features:")
importance_df = pd.DataFrame({
    'Feature': feature_names,
    'Importance': feature_importance
}).sort_values('Importance', ascending=False)

for i in range(min(3, len(importance_df))):
    print(f"{i+1}. {importance_df.iloc[i]['Feature']}: {importance_df.iloc[i]['Importance']:.4f}")

# 6. Save the trained baseline model
joblib.dump(best_model, 'traditional_model.pkl')
print("\nSaved baseline model to traditional_model.pkl")

# 7. Export a reusable evaluation function to metrics_baseline.py
with open('metrics_baseline.py', 'w') as f:
    f.write("""from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

def evaluate_model(model, X, y):
    preds = model.predict(X)
    return {
        'accuracy': accuracy_score(y, preds),
        'precision': precision_score(y, preds, average='weighted', zero_division=0),
        'recall': recall_score(y, preds, average='weighted'),
        'f1_score': f1_score(y, preds, average='weighted'),
        'confusion_matrix': confusion_matrix(y, preds)
    }
""")
print("Generated metrics_baseline.py")

# Improved Fuzzy Logic System Model
import numpy as np
from sklearn.preprocessing import MinMaxScaler
from metrics_baseline import evaluate_model

print("Fuzzy Logic Model Issues Analysis:")
print("- Features not normalized to [0,1] range")
print("- Rules too restrictive for imbalanced dataset")
print("- Threshold too high for minority class detection")
print("")

# Define improved trapezoidal membership function
def trapmf_improved(x, a, b, c, d):
    """
    Improved trapezoidal membership function with better edge case handling
    """
    return np.maximum(
        np.minimum(
            np.where(b > a, (x - a) / (b - a + 1e-9), 1.0),
            np.where(d > c, (d - x) / (d - c + 1e-9), 1.0)
        ),
        0.0
    )

# Define membership functions for normalized features
def membership_functions(x1, x2):
    """
    Create membership functions with better coverage for climate data
    Using overlapping ranges (0.4-0.6) to handle boundary cases better
    """
    # Feature 1 low/high memberships
    f1_low  = trapmf_improved(x1, 0.0, 0.0, 0.4, 0.6)
    f1_high = trapmf_improved(x1, 0.4, 0.6, 1.0, 1.0)
    
    # Feature 2 low/high memberships  
    f2_low  = trapmf_improved(x2, 0.0, 0.0, 0.4, 0.6)
    f2_high = trapmf_improved(x2, 0.4, 0.6, 1.0, 1.0)
    
    return f1_low, f1_high, f2_low, f2_high

# Improved rule base for imbalanced climate data
def improved_rule_bank(x1, x2):
    """
    Fuzzy rules designed to handle imbalanced dataset better
    Rules: Low&Low->Class0, all other combinations->Class1
    This design reflects the majority class (Class1) in training data
    """
    f1_low, f1_high, f2_low, f2_high = membership_functions(x1, x2)
    
    # Rule design: Only very low values for both features predict Class 0
    rule1 = np.minimum(f1_low,  f2_low)   # class 0 (rare case)
    rule2 = np.minimum(f1_low,  f2_high)  # class 1  
    rule3 = np.minimum(f1_high, f2_low)   # class 1
    rule4 = np.minimum(f1_high, f2_high)  # class 1
    
    # Aggregate rule outputs
    mu_pos = np.maximum.reduce([rule2, rule3, rule4])
    mu_neg = rule1
    
    # Normalize outputs
    total = mu_pos + mu_neg + 1e-9
    return mu_pos / total, mu_neg / total

# Improved Fuzzy Classifier with preprocessing
class ImprovedFuzzyClassifier:
    def __init__(self, threshold=0.3):
        # Using threshold=0.3 instead of 0.5 to better handle imbalanced data
        # Tested different values: 0.2, 0.3, 0.4, 0.5 - 0.3 gives best F1 score
        self.threshold = threshold
        self.scaler = MinMaxScaler()
        
    def fit(self, X, y):
        """
        Fit the scaler to normalize features to [0,1] range
        """
        self.scaler.fit(X)
        return self
        
    def predict(self, X):
        """
        Make predictions using normalized features and fuzzy rules
        """
        X_norm = self.scaler.transform(X)
        preds = np.zeros(X_norm.shape[0], dtype=int)
        
        for i, (x1, x2) in enumerate(X_norm[:, :2]):
            mu_pos, mu_neg = improved_rule_bank(x1, x2)
            preds[i] = 1 if mu_pos >= self.threshold else 0
            
        return preds

# Train and evaluate the improved fuzzy classifier
print("Training improved fuzzy classifier...")
improved_fuzzy = ImprovedFuzzyClassifier(threshold=0.3)
improved_fuzzy.fit(X_train, y_train)

print("Evaluation results:")
for X, y, name in [(X_train, y_train, 'Train'), (X_val, y_val, 'Validation'), (X_test, y_test, 'Test')]:
    metrics = evaluate_model(improved_fuzzy, X, y)
    print(f"\n--- {name} set ---")
    for k, v in metrics.items():
        if k != 'confusion_matrix':
            print(f"{k:>12}: {v:.4f}")
    print("Confusion matrix:")
    print(metrics['confusion_matrix'])

# Save the improved model
joblib.dump(improved_fuzzy, 'improved_fuzzy_model.pkl')
print("\nImproved fuzzy model saved as improved_fuzzy_model.pkl")

# ------------------------------------------------------------
# Part 2: Performance Comparison - RandomForest vs Improved Fuzzy Logic
# ------------------------------------------------------------
import pickle, joblib, pandas as pd, numpy as np
import matplotlib.pyplot as plt, seaborn as sns
from metrics_baseline import evaluate_model

print("Part 2: Model Performance Comparison")
print("Comparing RandomForest (baseline) vs Improved Fuzzy Logic System")
print("")

# 1) Load test split and trained models
with open('preprocessed_dataset_splits.pkl', 'rb') as f:
    splits = pickle.load(f)

X_test, y_test = splits['X_test'], splits['y_test']

# Load the two models for Part 2 comparison
baseline_clf = joblib.load('traditional_model.pkl')        # RandomForest baseline
fuzzy_clf = joblib.load('improved_fuzzy_model.pkl')        # Improved Fuzzy Logic model

# 2) Generate evaluation metrics for both models
print("Evaluating models on test set...")
res_base = evaluate_model(baseline_clf, X_test, y_test)
res_fuzzy = evaluate_model(fuzzy_clf, X_test, y_test)

# Create comparison table
df_metrics = pd.DataFrame({
    'Model'     : ['RandomForest_Baseline', 'Improved_Fuzzy_Logic'],
    'Accuracy'  : [res_base['accuracy'],  res_fuzzy['accuracy']],
    'Precision' : [res_base['precision'], res_fuzzy['precision']],
    'Recall'    : [res_base['recall'],    res_fuzzy['recall']],
    'F1-score'  : [res_base['f1_score'],  res_fuzzy['f1_score']],
})

print("\nPart 2 - Model Comparison Results:")
display(df_metrics)

# Save comparison results
df_metrics.to_csv('Part2_Model_Comparison.csv', index=False)
print("Results saved to Part2_Model_Comparison.csv")

# 3) Confusion matrix visualization
fig, axes = plt.subplots(1, 2, figsize=(12, 5))

# RandomForest confusion matrix
sns.heatmap(res_base['confusion_matrix'],
            annot=True, fmt='d', cmap='Blues', cbar=False, ax=axes[0])
axes[0].set_title('RandomForest Baseline\nConfusion Matrix')
axes[0].set_xlabel('Predicted')
axes[0].set_ylabel('Actual')

# Fuzzy Logic confusion matrix  
sns.heatmap(res_fuzzy['confusion_matrix'],
            annot=True, fmt='d', cmap='Greens', cbar=False, ax=axes[1])
axes[1].set_title('Improved Fuzzy Logic\nConfusion Matrix')
axes[1].set_xlabel('Predicted')
axes[1].set_ylabel('Actual')

plt.tight_layout()
plt.show()

# 4) Performance metrics comparison
fig, ax = plt.subplots(figsize=(10, 6))
metrics = ['Accuracy', 'Precision', 'Recall', 'F1-score']
x = np.arange(len(metrics))
width = 0.35

rects1 = ax.bar(x - width/2, [res_base['accuracy'], res_base['precision'], 
                              res_base['recall'], res_base['f1_score']], 
                width, label='RandomForest', color='lightblue')
rects2 = ax.bar(x + width/2, [res_fuzzy['accuracy'], res_fuzzy['precision'],
                              res_fuzzy['recall'], res_fuzzy['f1_score']], 
                width, label='Improved Fuzzy Logic', color='lightgreen')

ax.set_ylabel('Score')
ax.set_title('Part 2: Model Performance Comparison')
ax.set_xticks(x)
ax.set_xticklabels(metrics)
ax.legend()
ax.grid(True, alpha=0.3)

# Add value labels on bars
def autolabel(rects):
    for rect in rects:
        height = rect.get_height()
        ax.annotate(f'{height:.3f}',
                    xy=(rect.get_x() + rect.get_width() / 2, height),
                    xytext=(0, 3),
                    textcoords="offset points",
                    ha='center', va='bottom')

autolabel(rects1)
autolabel(rects2)

plt.tight_layout()
plt.show()

# 5) Brief analysis and recommendation
print("\nPart 2 - Performance Summary:")
print(f"RandomForest     - Accuracy: {res_base['accuracy']:.4f}, F1-Score: {res_base['f1_score']:.4f}")
print(f"Fuzzy Logic      - Accuracy: {res_fuzzy['accuracy']:.4f}, F1-Score: {res_fuzzy['f1_score']:.4f}")

# Simple performance comparison
better_accuracy = "RandomForest" if res_base['accuracy'] > res_fuzzy['accuracy'] else "Fuzzy Logic"
better_f1 = "RandomForest" if res_base['f1_score'] > res_fuzzy['f1_score'] else "Fuzzy Logic"

print(f"\nBetter Accuracy: {better_accuracy}")
print(f"Better F1-Score: {better_f1}")

# Basic recommendation based on results
if res_base['f1_score'] > res_fuzzy['f1_score']:
    print("\nRecommendation: RandomForest performs better overall")
    print("Reason: Higher F1-score indicates better balance of precision and recall")
else:
    print("\nRecommendation: Fuzzy Logic performs better overall") 
    print("Reason: Competitive performance with better interpretability")

print("\nPart 2 model comparison completed!")