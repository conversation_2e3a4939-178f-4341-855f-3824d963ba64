from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix

def evaluate_model(model, X, y):
    preds = model.predict(X)
    return {
        'accuracy': accuracy_score(y, preds),
        'precision': precision_score(y, preds, average='weighted', zero_division=0),
        'recall': recall_score(y, preds, average='weighted'),
        'f1_score': f1_score(y, preds, average='weighted'),
        'confusion_matrix': confusion_matrix(y, preds)
    }
