{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Climate Data Analysis Project\n", "\n", "This notebook contains comprehensive data preprocessing and exploratory data analysis for the climate simulation dataset.\n", "\n", "**Author:** Data Science Team  \n", "**Date:** 2025-06-28  \n", "**Dataset:** climate.csv  \n", "\n", "## Table of Contents\n", "1. [Setup and Data Loading](#section-1)\n", "2. [Data Preprocessing](#section-2)\n", "3. [Feature Engineering](#section-3)\n", "4. [Data Splitting](#section-4)\n", "5. [Exploratory Data Analysis (EDA)](#section-5)\n", "6. [Data Export](#section-6)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 1: Setup and Data Loading {#section-1}"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# Import required libraries\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "\n", "# Machine learning libraries\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.preprocessing import StandardScaler, RobustScaler\n", "from sklearn.ensemble import IsolationForest\n", "\n", "# Utility libraries\n", "import pickle\n", "import warnings\n", "import os\n", "from datetime import datetime\n", "\n", "# Configure settings\n", "warnings.filterwarnings('ignore')\n", "pd.set_option('display.max_columns', None)\n", "pd.set_option('display.precision', 4)\n", "plt.style.use('default')\n", "sns.set_palette(\"husl\")\n", "\n"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Loading climate dataset...\n", "==================================================\n", "   1. Study\n", "   2. <PERSON>\n", "   3. vconst_corr\n", "   4. vconst_2\n", "   5. vconst_3\n", "   6. vconst_4\n", "   7. vconst_5\n", "   8. vconst_7\n", "   9. ah_corr\n", "  10. ah_bolus\n", "  11. slm_corr\n", "  12. efficiency_factor\n", "  13. tidal_mix_max\n", "  14. vertical_decay_scale\n", "  15. convect_corr\n", "  16. bckgrnd_vdc1\n", "  17. bckgrnd_vdc_ban\n", "  18. bckgrnd_vdc_eq\n", "  19. bckgrnd_vdc_psim\n", "  20. <PERSON><PERSON><PERSON>\n", "\n", " First 3 rows:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Study</th>\n", "      <th>Run</th>\n", "      <th>vconst_corr</th>\n", "      <th>vconst_2</th>\n", "      <th>vconst_3</th>\n", "      <th>vconst_4</th>\n", "      <th>vconst_5</th>\n", "      <th>vconst_7</th>\n", "      <th>ah_corr</th>\n", "      <th>ah_bolus</th>\n", "      <th>slm_corr</th>\n", "      <th>efficiency_factor</th>\n", "      <th>tidal_mix_max</th>\n", "      <th>vertical_decay_scale</th>\n", "      <th>convect_corr</th>\n", "      <th>bckgrnd_vdc1</th>\n", "      <th>bckgrnd_vdc_ban</th>\n", "      <th>bckgrnd_vdc_eq</th>\n", "      <th>bckgrnd_vdc_psim</th>\n", "      <th><PERSON><PERSON><PERSON></th>\n", "      <th>outcome</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0.8590</td>\n", "      <td>0.9278</td>\n", "      <td>0.2529</td>\n", "      <td>0.2988</td>\n", "      <td>0.1705</td>\n", "      <td>0.7359</td>\n", "      <td>0.4283</td>\n", "      <td>0.5679</td>\n", "      <td>0.4744</td>\n", "      <td>0.2457</td>\n", "      <td>0.1042</td>\n", "      <td>0.8691</td>\n", "      <td>0.9975</td>\n", "      <td>0.4486</td>\n", "      <td>0.3075</td>\n", "      <td>0.8583</td>\n", "      <td>0.7970</td>\n", "      <td>0.8699</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>1</td>\n", "      <td>2</td>\n", "      <td>0.6060</td>\n", "      <td>0.4577</td>\n", "      <td>0.3594</td>\n", "      <td>0.3070</td>\n", "      <td>0.8433</td>\n", "      <td>0.9349</td>\n", "      <td>0.4446</td>\n", "      <td>0.8280</td>\n", "      <td>0.2966</td>\n", "      <td>0.6169</td>\n", "      <td>0.9758</td>\n", "      <td>0.9143</td>\n", "      <td>0.8452</td>\n", "      <td>0.8642</td>\n", "      <td>0.3467</td>\n", "      <td>0.3566</td>\n", "      <td>0.4384</td>\n", "      <td>0.5123</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>1</td>\n", "      <td>3</td>\n", "      <td>0.9976</td>\n", "      <td>0.3732</td>\n", "      <td>0.5174</td>\n", "      <td>0.5050</td>\n", "      <td>0.6189</td>\n", "      <td>0.6056</td>\n", "      <td>0.7462</td>\n", "      <td>0.1959</td>\n", "      <td>0.8157</td>\n", "      <td>0.6794</td>\n", "      <td>0.8034</td>\n", "      <td>0.6440</td>\n", "      <td>0.7184</td>\n", "      <td>0.9248</td>\n", "      <td>0.3154</td>\n", "      <td>0.2506</td>\n", "      <td>0.2856</td>\n", "      <td>0.3659</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Study  Run  vconst_corr  vconst_2  vconst_3  vconst_4  vconst_5  vconst_7  \\\n", "0      1    1       0.8590    0.9278    0.2529    0.2988    0.1705    0.7359   \n", "1      1    2       0.6060    0.4577    0.3594    0.3070    0.8433    0.9349   \n", "2      1    3       0.9976    0.3732    0.5174    0.5050    0.6189    0.6056   \n", "\n", "   ah_corr  ah_bolus  slm_corr  efficiency_factor  tidal_mix_max  \\\n", "0   0.4283    0.5679    0.4744             0.2457         0.1042   \n", "1   0.4446    0.8280    0.2966             0.6169         0.9758   \n", "2   0.7462    0.1959    0.8157             0.6794         0.8034   \n", "\n", "   vertical_decay_scale  convect_corr  bckgrnd_vdc1  bckgrnd_vdc_ban  \\\n", "0                0.8691        0.9975        0.4486           0.3075   \n", "1                0.9143        0.8452        0.8642           0.3467   \n", "2                0.6440        0.7184        0.9248           0.3154   \n", "\n", "   bckgrnd_vdc_eq  bckgrnd_vdc_psim  <PERSON>  outcome  \n", "0          0.8583            0.7970   0.8699        0  \n", "1          0.3566            0.4384   0.5123        1  \n", "2          0.2506            0.2856   0.3659        1  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Load the climate dataset\n", "print(\" Loading climate dataset...\")\n", "print(\"=\" * 50)\n", "\n", "# Check if file exists\n", "if not os.path.exists('C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹 (2)\\\\climate.xls'):\n", "    raise FileNotFoundError(\" climate.csv file not found in current directory\")\n", "\n", "# Load data\n", "df_raw = pd.read_csv('C:\\\\Users\\\\<USER>\\\\Desktop\\\\新建文件夹 (2)\\\\climate.xls')\n", "df = df_raw.copy()  # Working copy\n", "\n", "# Define columns\n", "target_column = 'outcome'\n", "feature_columns = [col for col in df.columns if col != target_column]\n", "\n", "# Basic information\n", "\n", "\n", "# Display basic info\n", "\n", "for i, col in enumerate(feature_columns, 1):\n", "    print(f\"  {i:2d}. {col}\")\n", "\n", "print(f\"\\n First 3 rows:\")\n", "display(df.head(3))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 2: Data Preprocessing {#section-2}"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Data Quality Assessment:\n", "==================================================\n", " Missing Values: 0 (0.00%)\n", " No missing values found\n", "\n", " Duplicate Rows: 0 (0.00%)\n", "\n", " Data Types:\n", "  float64: 18 columns\n", "  int64: 3 columns\n", "  Class 0: 46 (8.5%)\n", "  Class 1: 494 (91.5%)\n", " Dataset shows class imbalance\n"]}], "source": ["# Data quality assessment\n", "print(\" Data Quality Assessment:\")\n", "print(\"=\" * 50)\n", "\n", "# Missing values\n", "missing_counts = df.isnull().sum()\n", "total_missing = missing_counts.sum()\n", "print(f\" Missing Values: {total_missing:,} ({(total_missing/(len(df)*len(df.columns)))*100:.2f}%)\")\n", "\n", "if total_missing > 0:\n", "    missing_summary = pd.DataFrame({\n", "        'Missing_Count': missing_counts[missing_counts > 0],\n", "        'Missing_Percentage': (missing_counts[missing_counts > 0] / len(df)) * 100\n", "    })\n", "    display(missing_summary)\n", "else:\n", "    print(\" No missing values found\")\n", "\n", "# Duplicates\n", "duplicate_rows = df.duplicated().sum()\n", "print(f\"\\n Duplicate Rows: {duplicate_rows:,} ({(duplicate_rows/len(df))*100:.2f}%)\")\n", "\n", "# Data types\n", "print(f\"\\n Data Types:\")\n", "for dtype, count in df.dtypes.value_counts().items():\n", "    print(f\"  {dtype}: {count} columns\")\n", "\n", "# Target distribution\n", "\n", "target_dist = df[target_column].value_counts().sort_index()\n", "for value, count in target_dist.items():\n", "    pct = (count / len(df)) * 100\n", "    print(f\"  Class {value}: {count:,} ({pct:.1f}%)\")\n", "\n", "# Check for class imbalance\n", "imbalance_ratio = target_dist.max() / target_dist.min()\n", "\n", "if imbalance_ratio > 2:\n", "    print(\" Dataset shows class imbalance\")\n", "else:\n", "    print(\" Dataset is relatively balanced\")"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Data Cleaning:\n", "==================================================\n", " No missing values to handle\n", " No duplicates to remove\n", "\n", " Cleaning Summary:\n", "  Initial: 540 × 21\n", "  Final: 540 × 21\n", "  Retention: 100.0%\n", "\n", " Data cleaning completed!\n"]}], "source": ["# Data cleaning\n", "print(\" Data Cleaning:\")\n", "print(\"=\" * 50)\n", "\n", "df_cleaned = df.copy()\n", "initial_shape = df_cleaned.shape\n", "\n", "# Handle missing values (if any)\n", "if total_missing > 0:\n", "\n", "    for col in feature_columns:\n", "        if df_cleaned[col].isnull().sum() > 0:\n", "            median_val = df_cleaned[col].median()\n", "            df_cleaned[col].fillna(median_val, inplace=True)\n", "            print(f\"   {col}: filled with median {median_val:.4f}\")\n", "else:\n", "    print(\" No missing values to handle\")\n", "\n", "# Handle duplicates\n", "if duplicate_rows > 0:\n", "    df_cleaned = df_cleaned.drop_duplicates()\n", "    print(f\"   Duplicates removed\")\n", "else:\n", "    print(\" No duplicates to remove\")\n", "\n", "# Summary\n", "final_shape = df_cleaned.shape\n", "print(f\"\\n Cleaning Summary:\")\n", "print(f\"  Initial: {initial_shape[0]:,} × {initial_shape[1]}\")\n", "print(f\"  Final: {final_shape[0]:,} × {final_shape[1]}\")\n", "print(f\"  Retention: {(final_shape[0]/initial_shape[0])*100:.1f}%\")\n", "\n", "print(f\"\\n Data cleaning completed!\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 3: Data Splitting {#section-3}"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Dataset Splitting:\n", "==================================================\n", "\n", " Data for splitting:\n", "  Total samples: 540\n", "  Features: 20\n", "  Target classes: [np.int64(0), np.int64(1)]\n", "\n", " Split Results:\n", "  Training set: 324 samples (60.0%)\n", "  Validation set: 108 samples (20.0%)\n", "  Test set: 108 samples (20.0%)\n", "\n", " Target Distribution:\n", "  Train: Class 0: 28 (8.6%), Class 1: 296 (91.4%)\n", "  Validation: Class 0: 9 (8.3%), Class 1: 99 (91.7%)\n", "  Test: Class 0: 9 (8.3%), Class 1: 99 (91.7%)\n", "\n", " Dataset splitting completed!\n"]}], "source": ["# Dataset splitting\n", "print(\" Dataset Splitting:\")\n", "print(\"=\" * 50)\n", "\n", "# Prepare data for splitting (remove anomaly flag)\n", "data_for_split = df_cleaned.copy()\n", "X_all = data_for_split[feature_columns]\n", "y_all = data_for_split[target_column]\n", "\n", "print(f\"\\n Data for splitting:\")\n", "print(f\"  Total samples: {len(X_all):,}\")\n", "print(f\"  Features: {len(feature_columns)}\")\n", "print(f\"  Target classes: {sorted(y_all.unique())}\")\n", "\n", "# Split into train, validation, and test sets\n", "# First split: separate test set (20%)\n", "X_temp, X_test, y_temp, y_test = train_test_split(\n", "    X_all, y_all, test_size=0.2, random_state=42, stratify=y_all\n", ")\n", "\n", "# Second split: separate train and validation sets (60% train, 20% val)\n", "X_train, X_val, y_train, y_val = train_test_split(\n", "    X_temp, y_temp, test_size=0.25, random_state=42, stratify=y_temp\n", ")\n", "\n", "# Create splits dictionary\n", "data_splits = {\n", "    'X_train': X_train, 'y_train': y_train,\n", "    'X_val': X_val, 'y_val': y_val,\n", "    'X_test': X_test, 'y_test': y_test\n", "}\n", "\n", "# Display split information\n", "print(f\"\\n Split Results:\")\n", "print(f\"  Training set: {len(X_train):,} samples ({len(X_train)/len(X_all)*100:.1f}%)\")\n", "print(f\"  Validation set: {len(X_val):,} samples ({len(X_val)/len(X_all)*100:.1f}%)\")\n", "print(f\"  Test set: {len(X_test):,} samples ({len(X_test)/len(X_all)*100:.1f}%)\")\n", "\n", "# Check target distribution in each split\n", "print(f\"\\n Target Distribution:\")\n", "for split_name, y_split in [('Train', y_train), ('Validation', y_val), ('Test', y_test)]:\n", "    dist = y_split.value_counts().sort_index()\n", "    dist_str = ', '.join([f\"Class {k}: {v} ({v/len(y_split)*100:.1f}%)\" for k, v in dist.items()])\n", "    print(f\"  {split_name}: {dist_str}\")\n", "\n", "print(f\"\\n Dataset splitting completed!\")"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Split Visualization:\n", "==============================\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x400 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize dataset splits\n", "print(\" Split Visualization:\")\n", "print(\"=\" * 30)\n", "\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 4))\n", "\n", "# Split sizes pie chart\n", "split_sizes = [len(X_train), len(X_val), len(X_test)]\n", "split_labels = ['Training (60%)', 'Validation (20%)', 'Test (20%)']\n", "colors = ['lightblue', 'lightgreen', 'lightcoral']\n", "\n", "axes[0].pie(split_sizes, labels=split_labels, autopct='%1.1f%%', \n", "           colors=colors, startangle=90)\n", "axes[0].set_title('Dataset Split Proportions')\n", "\n", "# Target distribution across splits\n", "train_dist = y_train.value_counts().sort_index()\n", "val_dist = y_val.value_counts().sort_index()\n", "test_dist = y_test.value_counts().sort_index()\n", "\n", "x = np.arange(len(train_dist))\n", "width = 0.25\n", "\n", "axes[1].bar(x - width, train_dist.values, width, label='Training', color='lightblue')\n", "axes[1].bar(x, val_dist.values, width, label='Validation', color='lightgreen')\n", "axes[1].bar(x + width, test_dist.values, width, label='Test', color='lightcoral')\n", "\n", "axes[1].set_xlabel('Target Class')\n", "axes[1].set_ylabel('Count')\n", "axes[1].set_title('Target Distribution Across Splits')\n", "axes[1].set_xticks(x)\n", "axes[1].set_xticklabels(train_dist.index)\n", "axes[1].legend()\n", "axes[1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Section 4: Data Export {#section-4}"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": [" Saving Processed Data:\n", "==================================================\n", " Saved processed dataset: preprocessed_dataset.csv\n", " Saved processed dataset: preprocessed_dataset.pkl\n", " Saved data splits: preprocessed_dataset_splits.pkl\n", " Saved preprocessing objects: preprocessed_dataset_preprocessing_objects.pkl\n", "\n", " Export Summary:\n", "  Original data shape: (540, 21)\n", "  Final data shape: (540, 21)\n", "  Training samples: 324\n", "  Validation samples: 108\n", "  Test samples: 108\n", "  Features: 20\n", "  Target classes: [np.int64(0), np.int64(1)]\n", "\n", " Data preprocessing and analysis completed successfully!\n", " Output files: ['preprocessed_dataset.csv', 'preprocessed_dataset.pkl', 'preprocessed_dataset_splits.pkl', 'preprocessed_dataset_preprocessing_objects.pkl']\n"]}], "source": ["# Save processed data and objects\n", "print(\" Saving Processed Data:\")\n", "print(\"=\" * 50)\n", "\n", "# Prepare final dataset (without anomaly flag)\n", "final_data = df_cleaned.copy()\n", "\n", "# Save processed dataset\n", "output_files = {}\n", "\n", "# 1. Save as CSV\n", "csv_path = 'preprocessed_dataset.csv'\n", "final_data.to_csv(csv_path, index=False)\n", "output_files['csv'] = csv_path\n", "print(f\" Saved processed dataset: {csv_path}\")\n", "\n", "# 2. Save as pickle\n", "pkl_path = 'preprocessed_dataset.pkl'\n", "with open(pkl_path, 'wb') as f:\n", "    pickle.dump(final_data, f)\n", "output_files['pickle'] = pkl_path\n", "print(f\" Saved processed dataset: {pkl_path}\")\n", "\n", "# 3. Save train/val/test splits\n", "splits_path = 'preprocessed_dataset_splits.pkl'\n", "with open(splits_path, 'wb') as f:\n", "    pickle.dump(data_splits, f)\n", "output_files['splits'] = splits_path\n", "print(f\" Saved data splits: {splits_path}\")\n", "\n", "# 4. Save preprocessing objects\n", "preprocessing_objects = {\n", "    \n", "\n", "    'feature_columns': feature_columns,\n", "    'target_column': target_column,\n", "    'original_shape': df_raw.shape,\n", "    'final_shape': final_data.shape\n", "}\n", "\n", "objects_path = 'preprocessed_dataset_preprocessing_objects.pkl'\n", "with open(objects_path, 'wb') as f:\n", "    pickle.dump(preprocessing_objects, f)\n", "output_files['objects'] = objects_path\n", "print(f\" Saved preprocessing objects: {objects_path}\")\n", "\n", "# Summary\n", "print(f\"\\n Export Summary:\")\n", "print(f\"  Original data shape: {df_raw.shape}\")\n", "print(f\"  Final data shape: {final_data.shape}\")\n", "print(f\"  Training samples: {len(X_train):,}\")\n", "print(f\"  Validation samples: {len(X_val):,}\")\n", "print(f\"  Test samples: {len(X_test):,}\")\n", "print(f\"  Features: {len(feature_columns)}\")\n", "print(f\"  Target classes: {sorted(final_data[target_column].unique())}\")\n", "\n", "print(f\"\\n Data preprocessing and analysis completed successfully!\")\n", "print(f\" Output files: {list(output_files.values())}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Section 5: Baseline Model RandomForest"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Starting GridSearchCV on training data...\n", "Fitting 5 folds for each of 24 candidates, totalling 120 fits\n", "\n", "Best parameters: {'class_weight': 'balanced', 'max_depth': None, 'min_samples_split': 5, 'n_estimators': 100}\n", "Best CV weighted F1: 0.8901826626787546\n", "\n", "--- Validation Set Evaluation ---\n", "Accuracy : 0.9259\n", "Precision: 0.9315\n", "Recall   : 0.9259\n", "F1-score : 0.8977\n", "Confusion Matrix:\n", "[[ 1  8]\n", " [ 0 99]]\n", "\n", "--- Test Set Evaluation ---\n", "Accuracy : 0.9167\n", "Precision: 0.8403\n", "Recall   : 0.9167\n", "F1-score : 0.8768\n", "Confusion Matrix:\n", "[[ 0  9]\n", " [ 0 99]]\n", "\n", "Saved baseline model to traditional_model.pkl\n", "Generated metrics_baseline.py\n"]}], "source": ["import pickle\n", "import joblib\n", "from sklearn.ensemble import RandomForestClassifier\n", "from sklearn.model_selection import GridSearchCV\n", "from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "\n", "# 1. Load train/val/test splits produced\n", "with open('preprocessed_dataset_splits.pkl', 'rb') as f:\n", "    splits = pickle.load(f)\n", "X_train, y_train = splits['X_train'], splits['y_train']\n", "X_val,   y_val   = splits['X_val'],   splits['y_val']\n", "X_test,  y_test  = splits['X_test'],  splits['y_test']\n", "\n", "def evaluate_model(model, X, y, dataset_name):\n", "    \"\"\"\n", "    Evaluate the model on a given dataset and print accuracy, precision, recall, F1 and confusion matrix.\n", "    \"\"\"\n", "    preds = model.predict(X)\n", "    print(f\"\\n--- {dataset_name} Set Evaluation ---\")\n", "    print(f\"Accuracy : {accuracy_score(y, preds):.4f}\")\n", "    print(f\"Precision: {precision_score(y, preds, average='weighted', zero_division=0):.4f}\")\n", "    print(f\"Recall   : {recall_score(y, preds, average='weighted'):.4f}\")\n", "    print(f\"F1-score : {f1_score(y, preds, average='weighted'):.4f}\")\n", "    print(\"Confusion Matrix:\")\n", "    print(confusion_matrix(y, preds))\n", "\n", "# 2. Define hyperparameter grid for RandomForest\n", "param_grid = {\n", "    'n_estimators': [100, 200],\n", "    'max_depth': [None, 10, 20],\n", "    'min_samples_split': [2, 5],\n", "    'class_weight': ['balanced', None]\n", "}\n", "\n", "# 3. Perform GridSearchCV on training data\n", "rf = RandomForestClassifier(random_state=42)\n", "grid_search = GridSearchCV(\n", "    estimator=rf,\n", "    param_grid=param_grid,\n", "    cv=5,\n", "    scoring='f1_weighted',\n", "    n_jobs=-1,\n", "    verbose=2\n", ")\n", "print(\"Starting GridSearchCV on training data...\")\n", "grid_search.fit(X_train, y_train)\n", "\n", "best_model = grid_search.best_estimator_\n", "print(\"\\nBest parameters:\", grid_search.best_params_)\n", "print(\"Best CV weighted F1:\", grid_search.best_score_)\n", "\n", "# 4. Evaluate best model on validation and test sets\n", "evaluate_model(best_model, X_val, y_val, 'Validation')\n", "evaluate_model(best_model, X_test, y_test, 'Test')\n", "\n", "# 5. Save the trained baseline model\n", "joblib.dump(best_model, 'traditional_model.pkl')\n", "print(\"\\nSaved baseline model to traditional_model.pkl\")\n", "\n", "# 6. Export a reusable evaluation function to metrics_baseline.py\n", "with open('metrics_baseline.py', 'w') as f:\n", "    f.write(\"\"\"from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix\n", "\n", "def evaluate_model(model, X, y):\n", "    preds = model.predict(X)\n", "    return {\n", "        'accuracy': accuracy_score(y, preds),\n", "        'precision': precision_score(y, preds, average='weighted', zero_division=0),\n", "        'recall': recall_score(y, preds, average='weighted'),\n", "        'f1_score': f1_score(y, preds, average='weighted'),\n", "        'confusion_matrix': confusion_matrix(y, preds)\n", "    }\n", "\"\"\")\n", "print(\"Generated metrics_baseline.py\")\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Fuzzy Logic System Model"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "=== NumPy Fuzzy Classifier Evaluation ===\n", "\n", "--- Train set ---\n", "    accuracy: 0.0864\n", "   precision: 0.0075\n", "      recall: 0.0864\n", "    f1_score: 0.0137\n", "Confusion matrix:\n", " [[ 28   0]\n", " [296   0]]\n", "\n", "--- Validation set ---\n", "    accuracy: 0.0833\n", "   precision: 0.0069\n", "      recall: 0.0833\n", "    f1_score: 0.0128\n", "Confusion matrix:\n", " [[ 9  0]\n", " [99  0]]\n", "\n", "--- Test set ---\n", "    accuracy: 0.0833\n", "   precision: 0.0069\n", "      recall: 0.0833\n", "    f1_score: 0.0128\n", "Confusion matrix:\n", " [[ 9  0]\n", " [99  0]]\n", "\n", "Saved NumPy-based Fuzzy model → fuzzy_model.pkl\n"]}], "source": ["# fuzzy_numpy_classifier.py\n", "# ------------------------------------------------------------\n", "import pickle\n", "import joblib\n", "import numpy as np\n", "from metrics_baseline import evaluate_model    # reuse the unified evaluation script from member B\n", "\n", "# === 1. Load the splits generated by member A =================\n", "with open('preprocessed_dataset_splits.pkl', 'rb') as f:\n", "    splits = pickle.load(f)\n", "X_train, y_train = map(np.asarray, (splits['X_train'], splits['y_train']))\n", "X_val,   y_val   = map(np.asarray, (splits['X_val'],   splits['y_val']))\n", "X_test,  y_test  = map(np.asarray, (splits['X_test'],  splits['y_test']))\n", "\n", "# === 2. Define a trapezoidal membership function =============\n", "def trapmf(x, a, b, c, d):\n", "    return np.maximum(\n", "        np.minimum((x - a) / (b - a + 1e-9), np.minimum(1.0, (d - x) / (d - c + 1e-9))),\n", "        0.0\n", "    )\n", "\n", "# === 3. Low / High MFs for two features ======================\n", "# Thresholds can be based on quantiles or prior knowledge.\n", "MF = {\n", "    'feat1_low'  : lambda x: trapmf(x, 0.0, 0.0, 0.3, 0.5),\n", "    'feat1_high' : lambda x: trapmf(x, 0.3, 0.5, 1.0, 1.0),\n", "    'feat2_low'  : lambda x: trapmf(x, 0.0, 0.0, 0.3, 0.5),\n", "    'feat2_high' : lambda x: trapmf(x, 0.3, 0.5, 1.0, 1.0),\n", "}\n", "\n", "# === 4. Rule base (Antecedent → Consequent) ==================\n", "# Four sample rules that output a confidence μ_pos ∈ [0,1] for the positive class.\n", "def rule_bank(x1, x2):\n", "    μ = MF  # alias\n", "    # Antecedent membership degrees\n", "    f1_low   = μ['feat1_low'](x1)\n", "    f1_high  = μ['feat1_high'](x1)\n", "    f2_low   = μ['feat2_low'](x2)\n", "    f2_high  = μ['feat2_high'](x2)\n", "\n", "    # Mamdani inference: pos = max(min(...))\n", "    rule1 = np.minimum(f1_low,  f2_low)   # → negative\n", "    rule2 = np.minimum(f1_low,  f2_high)  # → positive\n", "    rule3 = np.minimum(f1_high, f2_low)   # → positive\n", "    rule4 = np.minimum(f1_high, f2_high)  # → positive\n", "\n", "    μ_pos = np.maximum.reduce([rule2, rule3, rule4])\n", "    μ_neg = np.maximum(rule1, 1e-9)       # avoid zero\n", "    # Optional normalization to [0,1]\n", "    s = μ_pos + μ_neg\n", "    μ_pos /= s\n", "    μ_neg /= s\n", "    return μ_pos, μ_neg\n", "\n", "# === 5. Wrap it as an sklearn-like classifier =================\n", "class FuzzyNumPyClassifier:\n", "    def __init__(self, threshold=0.5):\n", "        self.threshold = threshold\n", "\n", "    def fit(self, X, y):      # no training needed, but MF params could be tuned here\n", "        return self\n", "\n", "    def predict(self, X):\n", "        X = np.asarray(X)\n", "        preds = np.zeros(X.shape[0], dtype=int)\n", "        for i, (x1, x2) in enumerate(X[:, :2]):   # only first two features used\n", "            μ_pos, _ = rule_bank(x1, x2)\n", "            preds[i] = 1 if μ_pos >= self.threshold else 0\n", "        return preds\n", "\n", "# Instantiate and evaluate\n", "fuzzy_clf = FuzzyNumPyClassifier()\n", "print(\"\\n=== NumPy Fuzzy Classifier Evaluation ===\")\n", "for (X, y, name) in [\n", "    (X_train, y_train, 'Train'),\n", "    (X_val,   y_val,   'Validation'),\n", "    (X_test,  y_test,  'Test')\n", "]:\n", "    metrics = evaluate_model(fuzzy_clf, X, y)\n", "    print(f\"\\n--- {name} set ---\")\n", "    for k, v in metrics.items():\n", "        if k != 'confusion_matrix':\n", "            print(f\"{k:>12}: {v:.4f}\")\n", "    print(\"Confusion matrix:\\n\", metrics['confusion_matrix'])\n", "\n", "# === 6. Persist the model ====================================\n", "joblib.dump(fuzzy_clf, 'fuzzy_model.pkl')\n", "print(\"\\nSaved NumPy-based Fuzzy model → fuzzy_model.pkl\")\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Performance comparison"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Model</th>\n", "      <th>Accuracy</th>\n", "      <th>Precision</th>\n", "      <th>Recall</th>\n", "      <th>F1-score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Baseline_RF</td>\n", "      <td>0.9167</td>\n", "      <td>0.8403</td>\n", "      <td>0.9167</td>\n", "      <td>0.8768</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td><PERSON>zzy</td>\n", "      <td>0.0833</td>\n", "      <td>0.0069</td>\n", "      <td>0.0833</td>\n", "      <td>0.0128</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         Model  Accuracy  Precision  Recall  F1-score\n", "0  Baseline_RF    0.9167     0.8403  0.9167    0.8768\n", "1        Fuzzy    0.0833     0.0069  0.0833    0.0128"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1200x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 600x400 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# ------------------------------------------------------------\n", "# Performance Comparison – <PERSON><PERSON> vs. <PERSON>zzy/NN\n", "# ------------------------------------------------------------\n", "import pickle, joblib, pandas as pd, numpy as np\n", "import matplotlib.pyplot as plt, seaborn as sns\n", "from metrics_baseline import evaluate_model        # Re-use unified evaluation function (member B)\n", "\n", "# 1) Load test split and trained models -----------------------\n", "with open('preprocessed_dataset_splits.pkl', 'rb') as f:\n", "    splits = pickle.load(f)\n", "\n", "X_test, y_test = splits['X_test'], splits['y_test']\n", "\n", "baseline_clf = joblib.load('traditional_model.pkl')    # RandomForest baseline\n", "fuzzy_clf    = joblib.load('fuzzy_model.pkl')    # Fuzzy or Neural-Network model\n", "\n", "# 2) Generate evaluation metrics -----------------------------\n", "res_base = evaluate_model(baseline_clf, X_test, y_test)\n", "res_fuz  = evaluate_model(fuzzy_clf,    X_test, y_test)\n", "\n", "df_metrics = pd.DataFrame({\n", "    'Model'     : ['<PERSON><PERSON>_<PERSON>', 'Fuzzy'],\n", "    'Accuracy'  : [res_base['accuracy'],  res_fuz['accuracy']],\n", "    'Precision' : [res_base['precision'], res_fuz['precision']],\n", "    'Recall'    : [res_base['recall'],    res_fuz['recall']],\n", "    'F1-score'  : [res_base['f1_score'],  res_fuz['f1_score']],\n", "})\n", "display(df_metrics)                     # Show comparison table in a Notebook\n", "# df_metrics.to_csv('final_metrics_table.csv', index=False)  # uncomment to export\n", "\n", "# 3) Confusion-matrix visualisation ---------------------------\n", "fig, axes = plt.subplots(1, 2, figsize=(12, 5))\n", "for ax, res, title in zip(\n", "        axes,\n", "        [res_base, res_fuz],\n", "        ['<PERSON>ine RandomForest', 'Fuzzy']):\n", "    sns.heatmap(res['confusion_matrix'],\n", "                annot=True, fmt='d', cmap='Blues', cbar=False, ax=ax)\n", "    ax.set_title(f'{title} – Confusion Matrix')\n", "    ax.set_xlabel('Predicted'); ax.set_ylabel('Actual')\n", "plt.tight_layout(); plt.show()\n", "\n", "# 4) Error-distribution plot ---------------------------------\n", "pred_base = baseline_clf.predict(X_test)\n", "pred_fuz  = fuzzy_clf.predict(X_test)\n", "err_base  = (pred_base != y_test).astype(int)\n", "err_fuz   = (pred_fuz  != y_test).astype(int)\n", "\n", "fig, ax = plt.subplots(figsize=(6, 4))\n", "sns.histplot(err_base, binwidth=0.5, label='Baseline', ax=ax)\n", "sns.histplot(err_fuz,  binwidth=0.5, label='Fuzzy', ax=ax, alpha=0.7)\n", "ax.set_xticks([0, 1]); ax.set_xticklabels(['Correct', 'Incorrect'])\n", "ax.set_ylabel('Count'); ax.set_title('Error Distribution – Test Set')\n", "ax.legend(); plt.show()\n", "\n", "# 5) Save comparison table -----------------------------------\n", "df_metrics.to_csv('final_metrics_table.csv', index=False)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}}, "nbformat": 4, "nbformat_minor": 4}